-- 创建选课表（如果不存在）
DROP TABLE IF EXISTS `course_selection`;
CREATE TABLE `course_selection` (
  `selection_id` int NOT NULL AUTO_INCREMENT,
  `student_id` int DEFAULT NULL,
  `course_id` int DEFAULT NULL,
  `selection_time` datetime DEFAULT NULL,
  `status` varchar(1) DEFAULT '0',
  PRIMARY KEY (`selection_id`),
  UNIQUE KEY `UK_student_course` (`student_id`, `course_id`),
  KEY `FK_selection_student` (`student_id`),
  KEY `FK_selection_course` (`course_id`),
  CONSTRAINT `FK_selection_student` FOREIGN KEY (`student_id`) REFERENCES `student` (`person_id`),
  CONSTRAINT `FK_selection_course` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 插入选课示例数据
INSERT INTO `course_selection` VALUES
(1, 2, 1, '2025-01-05 10:00:00', '0'),
(2, 2, 2, '2025-01-05 10:05:00', '0'),
(3, 3, 1, '2025-01-05 11:00:00', '0'),
(4, 3, 4, '2025-01-05 11:05:00', '0');

-- 创建作业表
DROP TABLE IF EXISTS `homework`;
CREATE TABLE `homework` (
  `homework_id` int NOT NULL AUTO_INCREMENT,
  `student_id` int DEFAULT NULL,
  `course_id` int DEFAULT NULL,
  `content` varchar(1000) DEFAULT NULL,
  `assign_time` datetime DEFAULT NULL,
  `due_time` datetime DEFAULT NULL,
  `submit_time` datetime DEFAULT NULL,
  `status` varchar(2) DEFAULT '0',
  PRIMARY KEY (`homework_id`),
  KEY `FK_homework_student` (`student_id`),
  KEY `FK_homework_course` (`course_id`),
  CONSTRAINT `FK_homework_student` FOREIGN KEY (`student_id`) REFERENCES `student` (`person_id`),
  CONSTRAINT `FK_homework_course` FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- 插入作业示例数据
INSERT INTO `homework` VALUES
(1, 2, 1, '完成Java基础练习题1-10', '2025-01-10 09:00:00', '2025-01-15 23:59:59', NULL, '0'),
(2, 3, 1, '完成Java基础练习题1-10', '2025-01-10 09:00:00', '2025-01-15 23:59:59', '2025-01-12 15:30:00', '1'),
(3, 2, 2, '完成数学作业第三章习题', '2025-01-08 14:00:00', '2025-01-12 23:59:59', '2025-01-11 20:15:00', '2');

-- 更新菜单表，添加选课管理和作业管理菜单项
-- 注意：确保菜单ID不与现有菜单冲突，并且user_type_ids包含管理员角色
INSERT INTO `menu` VALUES (43, 'homework-panel', '作业管理', 4, '1')
ON DUPLICATE KEY UPDATE
name = VALUES(name),
title = VALUES(title),
pid = VALUES(pid),
user_type_ids = VALUES(user_type_ids);
