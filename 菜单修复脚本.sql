-- 菜单修复脚本
-- 用于修复前端菜单显示问题

-- 1. 查看当前菜单表结构和数据
SELECT * FROM menu ORDER BY pid, id;

-- 2. 检查是否存在作业管理菜单
SELECT * FROM menu WHERE name = 'homework-panel' OR title = '作业管理';

-- 3. 删除可能存在的重复菜单记录
DELETE FROM menu WHERE id = 43 AND name = 'homework-panel';

-- 4. 重新插入作业管理菜单（确保正确的格式）
INSERT INTO `menu` (id, name, title, pid, user_type_ids) VALUES 
(43, 'homework-panel', '作业管理', 4, '1');

-- 5. 验证插入结果
SELECT * FROM menu WHERE pid = 4 ORDER BY id;

-- 6. 检查教务管理父菜单是否存在
SELECT * FROM menu WHERE id = 4;

-- 7. 如果教务管理菜单不存在，创建它
INSERT IGNORE INTO `menu` (id, name, title, pid, user_type_ids) VALUES 
(4, NULL, '教务管理', NULL, '1');

-- 8. 查看完整的菜单层次结构
SELECT 
    m1.id as parent_id,
    m1.title as parent_title,
    m2.id as child_id,
    m2.name as child_name,
    m2.title as child_title,
    m2.user_type_ids
FROM menu m1 
LEFT JOIN menu m2 ON m1.id = m2.pid 
WHERE m1.pid IS NULL 
ORDER BY m1.id, m2.id;

-- 9. 检查用户角色表，确认角色ID
SELECT * FROM user_type;

-- 10. 检查当前登录用户的角色
-- SELECT u.*, ut.name as role_name 
-- FROM user u 
-- JOIN user_type ut ON u.user_type_id = ut.id 
-- WHERE u.person_id = ?; -- 替换为实际的person_id
