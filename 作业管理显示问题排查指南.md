# 作业管理显示问题排查指南

## 🔍 问题现象
在作业管理界面添加作业后，数据不显示在表格中。

## 📋 排查步骤

### 步骤1：检查数据库
1. **执行诊断脚本**：
   ```sql
   -- 检查homework表是否存在
   SHOW TABLES LIKE 'homework';
   
   -- 查看表结构
   DESCRIBE homework;
   
   -- 查看所有作业数据
   SELECT * FROM homework;
   ```

2. **验证数据是否保存成功**：
   - 添加作业后立即查询数据库
   - 确认数据已正确插入

### 步骤2：检查后端日志
1. **查看控制台输出**：
   - 启动后端时观察控制台
   - 添加作业时查看是否有错误信息

2. **验证API接口**：
   ```bash
   # 测试获取作业列表API
   curl -X POST http://localhost:22223/api/homework/getHomeworkList \
   -H "Content-Type: application/json" \
   -H "Authorization: Bearer YOUR_TOKEN" \
   -d '{"numName":""}'
   ```

### 步骤3：检查前端调试信息
1. **查看前端控制台**：
   - 添加作业后查看控制台输出
   - 确认请求数据格式正确

2. **验证数据加载**：
   - 检查"加载到的作业数量"输出
   - 确认作业数据格式正确

### 步骤4：检查数据映射
1. **学生数据映射**：
   - 确认学生数据包含`studentId`或`personId`字段
   - 验证下拉框选择的数据格式

2. **课程数据映射**：
   - 确认课程数据包含`courseId`字段
   - 验证下拉框选择的数据格式

## 🛠️ 常见问题及解决方案

### 问题1：数据库表不存在
**现象**：后端报错"Table 'homework' doesn't exist"
**解决**：执行建表脚本
```sql
source homework-table.sql;
```

### 问题2：外键约束错误
**现象**：保存时报外键约束错误
**解决**：
1. 确认student表和course表有数据
2. 检查student_id和course_id是否正确

### 问题3：权限问题
**现象**：API返回403错误
**解决**：确认用户具有ADMIN角色权限

### 问题4：数据映射错误
**现象**：添加成功但数据为空
**解决**：
1. 检查前端发送的studentId和courseId
2. 确认字段名称匹配

### 问题5：前端缓存问题
**现象**：数据库有数据但界面不显示
**解决**：
1. 重启前端应用
2. 手动调用loadAllHomeworks()方法

## 🔧 调试技巧

### 1. 启用详细日志
在application.properties中添加：
```properties
logging.level.cn.edu.sdu.java.server=DEBUG
logging.level.org.hibernate.SQL=DEBUG
```

### 2. 前端调试
在浏览器开发者工具中查看网络请求：
- 检查请求URL和参数
- 查看响应数据格式

### 3. 数据库调试
直接在数据库中插入测试数据：
```sql
INSERT INTO homework (student_id, course_id, content, assign_time, status) 
VALUES (2, 1, '测试作业', NOW(), '0');
```

## ✅ 验证清单

- [ ] homework表已创建并有正确的结构
- [ ] student表和course表有测试数据
- [ ] 后端服务正常启动，无错误日志
- [ ] API接口可以正常访问
- [ ] 前端能够加载学生和课程下拉框数据
- [ ] 添加作业时前端发送正确的请求数据
- [ ] 后端能够正确保存作业数据
- [ ] 前端能够正确加载和显示作业列表

## 🚀 快速修复方案

如果以上步骤都正常，尝试以下快速修复：

1. **重启应用**：
   - 停止后端和前端
   - 重新启动后端
   - 重新启动前端

2. **清空缓存**：
   - 清除浏览器缓存
   - 重新编译前端代码

3. **手动刷新**：
   - 在作业管理界面点击"查询作业"按钮
   - 检查是否显示数据

4. **检查网络**：
   - 确认前后端网络连接正常
   - 检查防火墙设置

按照这个指南逐步排查，应该能够找到并解决问题。
