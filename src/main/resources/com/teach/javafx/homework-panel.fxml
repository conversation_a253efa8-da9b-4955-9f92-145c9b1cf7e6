<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
            style="-fx-background-color: #f8f9fa;" fx:controller="com.teach.javafx.controller.HomeworkController">
   <top>
      <VBox spacing="15" style="-fx-background-color: white; -fx-padding: 20; -fx-border-color: #e0e0e0; -fx-border-width: 0 0 1 0; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 1);">
         <children>
            <Label text="作业管理" style="-fx-font-size: 24; -fx-font-weight: bold; -fx-text-fill: #2c3e50;"/>
            
            <!-- 查询区域 -->
            <HBox spacing="15" alignment="CENTER_LEFT">
               <children>
                  <Label text="查询条件:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <TextField fx:id="numNameTextField" promptText="请输入学号、姓名、课程编号或课程名称" prefWidth="300" style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>
                  <Button fx:id="queryButton" text="查询作业" onAction="#onQueryButtonClick" style="-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 10 20; -fx-cursor: hand;"/>
               </children>
            </HBox>
            
            <!-- 操作区域 -->
            <HBox spacing="15" alignment="CENTER_LEFT">
               <children>
                  <Label text="添加作业:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <Label text="学生:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <ComboBox fx:id="studentComboBox" prefWidth="200" promptText="请选择学生" style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 7;"/>
                  <Label text="课程:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <ComboBox fx:id="courseComboBox" prefWidth="200" promptText="请选择课程" style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 7;"/>
               </children>
            </HBox>
            
            <HBox spacing="15" alignment="CENTER_LEFT">
               <children>
                  <Label text="作业内容:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <TextArea fx:id="contentTextArea" promptText="请输入作业内容" prefWidth="400" prefHeight="60" style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>
                  <Label text="截止时间:" style="-fx-font-weight: bold; -fx-text-fill: #555;" />
                  <TextField fx:id="dueTimeField" promptText="yyyy-MM-dd HH:mm:ss" prefWidth="150" style="-fx-background-radius: 4; -fx-border-radius: 4; -fx-border-color: #ddd; -fx-padding: 8;"/>
               </children>
            </HBox>
            
            <HBox spacing="15" alignment="CENTER_RIGHT">
               <children>
                  <Button fx:id="addButton" text="添加作业" onAction="#onAddButtonClick" style="-fx-background-color: #4CAF50; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 10 20; -fx-cursor: hand;"/>
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- 表格区域 -->
      <TableView fx:id="dataTableView" style="-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-width: 1; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 1);">
         <columns>
            <TableColumn fx:id="studentNumColumn" prefWidth="100.0" text="学号" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="studentNameColumn" prefWidth="100.0" text="姓名" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="classNameColumn" prefWidth="120.0" text="班级" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="majorColumn" prefWidth="150.0" text="专业" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="courseNameColumn" prefWidth="180.0" text="课程名称" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="contentColumn" prefWidth="250.0" text="作业内容" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
            <TableColumn fx:id="assignTimeColumn" prefWidth="150.0" text="布置时间" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
            <TableColumn fx:id="dueTimeColumn" prefWidth="150.0" text="截止时间" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
            <TableColumn fx:id="statusColumn" prefWidth="100.0" text="状态" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
            <TableColumn fx:id="operateColumn" prefWidth="180.0" text="操作" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
         </columns>
         <padding>
            <Insets top="10" right="10" bottom="10" left="10"/>
         </padding>
         <BorderPane.margin>
            <Insets top="20" right="20" bottom="20" left="20"/>
         </BorderPane.margin>
      </TableView>
   </center>
</BorderPane>
