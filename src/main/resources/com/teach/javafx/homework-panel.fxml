<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
            style="-fx-background-color: #f5f7fa;" fx:controller="com.teach.javafx.controller.HomeworkController">
   <top>
      <VBox spacing="20" style="-fx-background-color: white; -fx-padding: 25; -fx-border-color: #e1e8ed; -fx-border-width: 0 0 2 0; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.08), 8, 0, 0, 2);">
         <children>
            <!-- 标题区域 -->
            <HBox alignment="CENTER_LEFT" spacing="10">
               <children>
                  <Label text="📚" style="-fx-font-size: 28;"/>
                  <Label text="作业管理" style="-fx-font-size: 28; -fx-font-weight: bold; -fx-text-fill: #2c3e50;"/>
                  <Region HBox.hgrow="ALWAYS"/>
                  <Label text="管理学生作业信息" style="-fx-font-size: 14; -fx-text-fill: #7f8c8d;"/>
               </children>
            </HBox>

            <!-- 查询区域 -->
            <VBox spacing="12" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-background-radius: 8; -fx-border-color: #e9ecef; -fx-border-width: 1; -fx-border-radius: 8;">
               <children>
                  <Label text="🔍 查询作业" style="-fx-font-size: 16; -fx-font-weight: bold; -fx-text-fill: #495057;"/>
                  <HBox spacing="15" alignment="CENTER_LEFT">
                     <children>
                        <Label text="查询条件:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d; -fx-min-width: 80;" />
                        <TextField fx:id="numNameTextField" promptText="请输入学号、姓名、课程编号或课程名称" prefWidth="350"
                                   style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-padding: 10; -fx-font-size: 14;"/>
                        <Button fx:id="queryButton" text="查询作业" onAction="#onQueryButtonClick"
                                style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 6; -fx-padding: 10 20; -fx-cursor: hand; -fx-font-size: 14;"/>
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- 添加作业区域 -->
            <VBox spacing="15" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-background-radius: 8; -fx-border-color: #e9ecef; -fx-border-width: 1; -fx-border-radius: 8;">
               <children>
                  <Label text="➕ 添加新作业" style="-fx-font-size: 16; -fx-font-weight: bold; -fx-text-fill: #495057;"/>

                  <!-- 第一行：学生和课程选择 -->
                  <GridPane hgap="20" vgap="15">
                     <columnConstraints>
                        <ColumnConstraints minWidth="80" prefWidth="80"/>
                        <ColumnConstraints minWidth="250" prefWidth="250"/>
                        <ColumnConstraints minWidth="80" prefWidth="80"/>
                        <ColumnConstraints minWidth="250" prefWidth="250"/>
                     </columnConstraints>
                     <children>
                        <Label text="选择学生:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                        <ComboBox fx:id="studentComboBox" prefWidth="250" promptText="请选择学生"
                                  style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-padding: 8; -fx-font-size: 14;"
                                  GridPane.columnIndex="1" GridPane.rowIndex="0"/>

                        <Label text="选择课程:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;" GridPane.columnIndex="2" GridPane.rowIndex="0"/>
                        <ComboBox fx:id="courseComboBox" prefWidth="250" promptText="请选择课程"
                                  style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-padding: 8; -fx-font-size: 14;"
                                  GridPane.columnIndex="3" GridPane.rowIndex="0"/>
                     </children>
                  </GridPane>

                  <!-- 第二行：作业内容 -->
                  <VBox spacing="8">
                     <children>
                        <Label text="作业内容:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;"/>
                        <TextArea fx:id="contentTextArea" promptText="请详细描述作业要求和内容..." prefHeight="80" wrapText="true"
                                  style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-padding: 10; -fx-font-size: 14;"/>
                     </children>
                  </VBox>

                  <!-- 第三行：时间选择 -->
                  <GridPane hgap="20" vgap="10">
                     <columnConstraints>
                        <ColumnConstraints minWidth="80" prefWidth="80"/>
                        <ColumnConstraints minWidth="150" prefWidth="150"/>
                        <ColumnConstraints minWidth="80" prefWidth="80"/>
                        <ColumnConstraints minWidth="150" prefWidth="150"/>
                        <ColumnConstraints minWidth="80" prefWidth="80"/>
                        <ColumnConstraints minWidth="150" prefWidth="150"/>
                     </columnConstraints>
                     <children>
                        <Label text="截止日期:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;" GridPane.columnIndex="0" GridPane.rowIndex="0"/>
                        <DatePicker fx:id="dueDatePicker" promptText="选择日期" prefWidth="150"
                                    style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-font-size: 14;"
                                    GridPane.columnIndex="1" GridPane.rowIndex="0"/>

                        <Label text="截止时间:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;" GridPane.columnIndex="2" GridPane.rowIndex="0"/>
                        <HBox spacing="5" GridPane.columnIndex="3" GridPane.rowIndex="0">
                           <children>
                              <ComboBox fx:id="hourComboBox" prefWidth="60" promptText="时"
                                        style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-font-size: 14;"/>
                              <Label text=":" style="-fx-font-weight: bold; -fx-text-fill: #6c757d; -fx-alignment: center;"/>
                              <ComboBox fx:id="minuteComboBox" prefWidth="60" promptText="分"
                                        style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-font-size: 14;"/>
                           </children>
                        </HBox>

                        <Label text="作业状态:" style="-fx-font-weight: bold; -fx-text-fill: #6c757d;" GridPane.columnIndex="4" GridPane.rowIndex="0"/>
                        <ComboBox fx:id="statusComboBox" prefWidth="150" value="未提交"
                                  style="-fx-background-radius: 6; -fx-border-radius: 6; -fx-border-color: #ced4da; -fx-font-size: 14;"
                                  GridPane.columnIndex="5" GridPane.rowIndex="0"/>
                     </children>
                  </GridPane>

                  <!-- 时间显示区域 -->
                  <HBox spacing="15" alignment="CENTER_LEFT" style="-fx-background-color: #e3f2fd; -fx-padding: 10; -fx-background-radius: 6; -fx-border-color: #2196f3; -fx-border-width: 1; -fx-border-radius: 6;">
                     <children>
                        <Label text="📅" style="-fx-font-size: 16;"/>
                        <Label text="当前选择的截止时间:" style="-fx-font-weight: bold; -fx-text-fill: #1976d2; -fx-font-size: 14;"/>
                        <Label fx:id="selectedTimeLabel" text="2025-01-27 23:55:00" style="-fx-font-weight: bold; -fx-text-fill: #0d47a1; -fx-font-size: 16; -fx-background-color: white; -fx-padding: 5 10; -fx-background-radius: 4; -fx-border-color: #2196f3; -fx-border-width: 1; -fx-border-radius: 4;"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Button fx:id="refreshTimeButton" text="🔄 刷新时间" onAction="#onRefreshTimeButtonClick"
                                style="-fx-background-color: #2196f3; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 10; -fx-cursor: hand; -fx-font-size: 12;"/>
                     </children>
                  </HBox>



                  <!-- 操作按钮 -->
                  <HBox spacing="15" alignment="CENTER_RIGHT" style="-fx-padding: 10 0 0 0;">
                     <children>
                        <Button fx:id="clearButton" text="清空" onAction="#onClearButtonClick"
                                style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 6; -fx-padding: 10 20; -fx-cursor: hand; -fx-font-size: 14;"/>
                        <Button fx:id="addButton" text="添加作业" onAction="#onAddButtonClick"
                                style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 6; -fx-padding: 10 25; -fx-cursor: hand; -fx-font-size: 14;"/>
                     </children>
                  </HBox>
               </children>
            </VBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <!-- 表格区域 -->
      <VBox style="-fx-background-color: white; -fx-padding: 15; -fx-background-radius: 8; -fx-border-color: #e9ecef; -fx-border-width: 1; -fx-border-radius: 8; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.08), 8, 0, 0, 2);" VBox.vgrow="ALWAYS">
         <children>
            <!-- 表格标题和工具栏 -->
            <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 0 0 10 0;">
               <children>
                  <Label text="📋" style="-fx-font-size: 20;"/>
                  <Label text="作业列表" style="-fx-font-size: 20; -fx-font-weight: bold; -fx-text-fill: #495057;"/>
                  <Region HBox.hgrow="ALWAYS"/>
                  <Label fx:id="totalCountLabel" text="共 0 条记录" style="-fx-font-size: 14; -fx-text-fill: #6c757d; -fx-background-color: #f8f9fa; -fx-padding: 5 10; -fx-background-radius: 15;"/>
                  <Button fx:id="refreshListButton" text="🔄 刷新列表" onAction="#onRefreshListButtonClick"
                          style="-fx-background-color: #17a2b8; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 10; -fx-cursor: hand; -fx-font-size: 12;"/>
               </children>
            </HBox>

            <!-- 表格 -->
            <TableView fx:id="dataTableView" VBox.vgrow="ALWAYS"
                       style="-fx-background-color: transparent; -fx-border-color: #dee2e6; -fx-border-width: 1; -fx-border-radius: 6;"
                       prefHeight="400" minHeight="300">
               <columns>
                  <TableColumn fx:id="studentNumColumn" prefWidth="80.0" minWidth="60.0" text="学号" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="studentNameColumn" prefWidth="80.0" minWidth="60.0" text="姓名" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="classNameColumn" prefWidth="100.0" minWidth="80.0" text="班级" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="majorColumn" prefWidth="120.0" minWidth="100.0" text="专业" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="courseNameColumn" prefWidth="140.0" minWidth="120.0" text="课程名称" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="contentColumn" prefWidth="200.0" minWidth="150.0" text="作业内容" style="-fx-alignment: CENTER-LEFT; -fx-font-weight: bold;" />
                  <TableColumn fx:id="assignTimeColumn" prefWidth="120.0" minWidth="100.0" text="布置时间" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="dueTimeColumn" prefWidth="120.0" minWidth="100.0" text="截止时间" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="statusColumn" prefWidth="80.0" minWidth="60.0" text="状态" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
                  <TableColumn fx:id="operateColumn" prefWidth="140.0" minWidth="120.0" text="操作" style="-fx-alignment: CENTER; -fx-font-weight: bold;" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>

            <!-- 表格底部信息栏 -->
            <HBox alignment="CENTER_LEFT" spacing="15" style="-fx-padding: 10 0 0 0; -fx-border-color: #e9ecef; -fx-border-width: 1 0 0 0;">
               <children>
                  <Label text="💡" style="-fx-font-size: 14;"/>
                  <Label text="提示：双击表格行可快速编辑，右键可查看更多操作" style="-fx-font-size: 12; -fx-text-fill: #6c757d;"/>
                  <Region HBox.hgrow="ALWAYS"/>
                  <Label fx:id="lastUpdateLabel" text="最后更新：--" style="-fx-font-size: 11; -fx-text-fill: #adb5bd;"/>
               </children>
            </HBox>
         </children>
         <BorderPane.margin>
            <Insets top="10" right="15" bottom="15" left="15"/>
         </BorderPane.margin>
      </VBox>
   </center>
</BorderPane>
