package com.teach.javafx.controller;

import com.teach.javafx.request.DataRequest;
import com.teach.javafx.request.DataResponse;
import com.teach.javafx.request.HttpRequestUtil;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.control.cell.MapValueFactory;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.FlowPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import javafx.util.StringConverter;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * HomeworkController 作业管理交互控制类 对应 homework-panel.fxml
 */
public class HomeworkController {
    @FXML
    private TableView<Map<String, Object>> dataTableView;
    @FXML
    private TableColumn<Map, String> studentNumColumn;
    @FXML
    private TableColumn<Map, String> studentNameColumn;
    @FXML
    private TableColumn<Map, String> classNameColumn;
    @FXML
    private TableColumn<Map, String> majorColumn;
    @FXML
    private TableColumn<Map, String> courseNameColumn;
    @FXML
    private TableColumn<Map, String> contentColumn;
    @FXML
    private TableColumn<Map, String> assignTimeColumn;
    @FXML
    private TableColumn<Map, String> dueTimeColumn;
    @FXML
    private TableColumn<Map, String> statusColumn;
    @FXML
    private TableColumn<Map, FlowPane> operateColumn;

    @FXML
    private TextField numNameTextField;
    @FXML
    private ComboBox<Map<String, Object>> studentComboBox;
    @FXML
    private ComboBox<Map<String, Object>> courseComboBox;
    @FXML
    private TextArea contentTextArea;
    @FXML
    private TextField dueTimeField;

    private List<Map<String, Object>> homeworkList = new ArrayList<>();
    private final ObservableList<Map<String, Object>> observableList = FXCollections.observableArrayList();
    private List<Map<String, Object>> allStudentList = new ArrayList<>();
    private List<Map<String, Object>> allCourseList = new ArrayList<>();

    @FXML
    private void onQueryButtonClick() {
        String numName = numNameTextField.getText();

        DataRequest req = new DataRequest();
        req.add("numName", numName);
        DataResponse res = HttpRequestUtil.request("/api/homework/getHomeworkList", req);

        if (res != null && res.getCode() == 0) {
            List<Map<String, Object>> queryResults = (List<Map<String, Object>>) res.getData();
            showQueryResultWindow(queryResults);
        } else {
            showAlert("错误", "查询失败: " + (res != null ? res.getMsg() : "网络错误"));
        }
    }

    private void showQueryResultWindow(List<Map<String, Object>> queryResults) {
        try {
            Stage stage = new Stage();
            stage.setTitle("作业查询结果");

            TableView<Map> tableView = new TableView<>();

            TableColumn<Map, String> studentNumCol = new TableColumn<>("学号");
            studentNumCol.setCellValueFactory(new MapValueFactory<>("studentNum"));
            studentNumCol.setPrefWidth(100.0);

            TableColumn<Map, String> studentNameCol = new TableColumn<>("姓名");
            studentNameCol.setCellValueFactory(new MapValueFactory<>("studentName"));
            studentNameCol.setPrefWidth(100.0);

            TableColumn<Map, String> classNameCol = new TableColumn<>("班级");
            classNameCol.setCellValueFactory(new MapValueFactory<>("className"));
            classNameCol.setPrefWidth(120.0);

            TableColumn<Map, String> majorCol = new TableColumn<>("专业");
            majorCol.setCellValueFactory(new MapValueFactory<>("major"));
            majorCol.setPrefWidth(150.0);

            TableColumn<Map, String> courseNameCol = new TableColumn<>("课程名称");
            courseNameCol.setCellValueFactory(new MapValueFactory<>("courseName"));
            courseNameCol.setPrefWidth(180.0);

            TableColumn<Map, String> contentCol = new TableColumn<>("作业内容");
            contentCol.setCellValueFactory(new MapValueFactory<>("content"));
            contentCol.setPrefWidth(250.0);

            TableColumn<Map, String> assignTimeCol = new TableColumn<>("布置时间");
            assignTimeCol.setCellValueFactory(new MapValueFactory<>("assignTime"));
            assignTimeCol.setPrefWidth(150.0);

            TableColumn<Map, String> dueTimeCol = new TableColumn<>("截止时间");
            dueTimeCol.setCellValueFactory(new MapValueFactory<>("dueTime"));
            dueTimeCol.setPrefWidth(150.0);

            TableColumn<Map, String> statusCol = new TableColumn<>("状态");
            statusCol.setCellValueFactory(new MapValueFactory<>("statusName"));
            statusCol.setPrefWidth(100.0);

            tableView.getColumns().addAll(studentNumCol, studentNameCol, classNameCol, majorCol,
                                         courseNameCol, contentCol, assignTimeCol, dueTimeCol, statusCol);

            ObservableList<Map> data = FXCollections.observableArrayList();
            for (Map<String, Object> item : queryResults) {
                data.add(item);
            }
            tableView.setItems(data);

            BorderPane borderPane = new BorderPane();
            borderPane.setCenter(tableView);
            borderPane.setPadding(new Insets(20));

            Scene scene = new Scene(borderPane, 1200, 600);
            stage.setScene(scene);

            stage.setOnHidden(event -> {
                loadAllHomeworks();
            });

            stage.show();
        } catch (Exception e) {
            e.printStackTrace();
            showAlert("错误", "显示查询结果窗口失败: " + e.getMessage());
        }
    }

    /**
     * 加载所有作业记录
     */
    private void loadAllHomeworks() {
        DataRequest req = new DataRequest();
        req.add("numName", "");
        DataResponse res = HttpRequestUtil.request("/api/homework/getHomeworkList", req);

        if (res != null && res.getCode() == 0) {
            homeworkList = (List<Map<String, Object>>) res.getData();
            setTableViewData();
        }
    }

    @FXML
    private void onAddButtonClick() {
        Map<String, Object> selectedStudent = studentComboBox.getSelectionModel().getSelectedItem();
        Map<String, Object> selectedCourse = courseComboBox.getSelectionModel().getSelectedItem();
        String content = contentTextArea.getText();
        String dueTime = dueTimeField.getText();

        if (selectedStudent == null) {
            showAlert("错误", "请选择学生");
            return;
        }
        if (selectedCourse == null) {
            showAlert("错误", "请选择课程");
            return;
        }
        if (content == null || content.trim().isEmpty()) {
            showAlert("错误", "请输入作业内容");
            return;
        }

        // 创建请求
        DataRequest req = new DataRequest();
        req.add("studentId", selectedStudent.get("studentId"));
        req.add("courseId", selectedCourse.get("courseId"));
        req.add("content", content.trim());
        req.add("dueTime", dueTime);
        req.add("status", "0"); // 默认未提交状态

        // 发送请求
        DataResponse res = HttpRequestUtil.request("/api/homework/homeworkSave", req);
        if (res != null && res.getCode() == 0) {
            showAlert("成功", "作业添加成功");
            // 清空输入
            studentComboBox.getSelectionModel().clearSelection();
            courseComboBox.getSelectionModel().clearSelection();
            contentTextArea.clear();
            dueTimeField.clear();
            // 刷新列表
            loadAllHomeworks();
        } else {
            String errorMsg = res != null ? res.getMsg() : "作业添加失败";
            showAlert("错误", errorMsg);
        }
    }

    private void setTableViewData() {
        observableList.clear();
        Map<String, Object> map;
        FlowPane flowPane;
        Button editButton, deleteButton;

        for (Map<String, Object> homework : homeworkList) {
            map = new HashMap<>(homework);

            flowPane = new FlowPane();
            flowPane.setHgap(10);

            editButton = new Button("编辑");
            editButton.setOnAction(e -> onEditHomework(homework));
            editButton.setStyle("-fx-background-color: #FF9800; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 10;");

            deleteButton = new Button("删除");
            deleteButton.setOnAction(e -> onDeleteHomework(homework));
            deleteButton.setStyle("-fx-background-color: #f44336; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 4; -fx-padding: 5 10;");

            flowPane.getChildren().addAll(editButton, deleteButton);
            map.put("operate", flowPane);

            observableList.add(map);
        }

        studentNumColumn.setCellValueFactory(new MapValueFactory<>("studentNum"));
        studentNameColumn.setCellValueFactory(new MapValueFactory<>("studentName"));
        classNameColumn.setCellValueFactory(new MapValueFactory<>("className"));
        majorColumn.setCellValueFactory(new MapValueFactory<>("major"));
        courseNameColumn.setCellValueFactory(new MapValueFactory<>("courseName"));
        contentColumn.setCellValueFactory(new MapValueFactory<>("content"));
        assignTimeColumn.setCellValueFactory(new MapValueFactory<>("assignTime"));
        dueTimeColumn.setCellValueFactory(new MapValueFactory<>("dueTime"));
        statusColumn.setCellValueFactory(new MapValueFactory<>("statusName"));
        operateColumn.setCellValueFactory(new MapValueFactory<>("operate"));

        dataTableView.setItems(observableList);
    }

    private void onEditHomework(Map<String, Object> homeworkData) {
        // 实现编辑功能
        showEditDialog(homeworkData);
    }
