-- 作业管理问题诊断脚本

-- 1. 检查homework表是否存在
SHOW TABLES LIKE 'homework';

-- 2. 检查homework表结构
DESCRIBE homework;

-- 3. 查看homework表中的所有数据
SELECT * FROM homework;

-- 4. 检查student表数据
SELECT person_id, num, name, class_name, major FROM student s 
JOIN person p ON s.person_id = p.person_id 
LIMIT 5;

-- 5. 检查course表数据
SELECT course_id, num, name, credit FROM course LIMIT 5;

-- 6. 检查是否有外键约束问题
SELECT 
    h.homework_id,
    h.student_id,
    h.course_id,
    h.content,
    h.assign_time,
    h.due_time,
    h.status,
    s.person_id as student_person_id,
    p.name as student_name,
    c.name as course_name
FROM homework h
LEFT JOIN student s ON h.student_id = s.person_id
LEFT JOIN person p ON s.person_id = p.person_id
LEFT JOIN course c ON h.course_id = c.course_id;

-- 7. 测试插入一条数据
INSERT INTO homework (student_id, course_id, content, assign_time, due_time, status) 
VALUES (2, 1, '测试作业内容', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), '0');

-- 8. 验证插入结果
SELECT * FROM homework WHERE content = '测试作业内容';

-- 9. 检查最近插入的数据
SELECT * FROM homework ORDER BY homework_id DESC LIMIT 5;
