# 菜单添加操作指南

## 🎯 目标
通过前端界面正确添加"作业管理"菜单项

## 📋 操作步骤

### 步骤1：启动应用并登录
1. 启动后端服务（java-server）
2. 启动前端应用（java-fx）
3. 使用管理员账号登录

### 步骤2：进入菜单管理
1. 在主界面左侧菜单树中找到"系统管理"
2. 点击"菜单管理"进入菜单管理界面

### 步骤3：添加作业管理菜单
1. **选择父菜单**：
   - 在左侧菜单树中找到"教务管理"
   - 右键点击"教务管理"
   - 选择"添加"（或者选中后点击右侧的添加按钮）

2. **填写菜单信息**：
   - **菜单ID**: `43`
   - **菜单名**: `homework-panel`
   - **菜单标题**: `作业管理`
   - **角色权限**: 勾选"管理员"

3. **提交保存**：
   - 点击"提交"按钮
   - 等待"保存成功"提示

### 步骤4：验证菜单添加
1. 重启前端应用
2. 重新登录
3. 检查"教务管理"下是否出现"作业管理"菜单

## ⚠️ 注意事项

### 菜单ID选择
- 确保ID唯一，不与现有菜单冲突
- 建议使用43（如果已存在，可使用44、45等）

### 菜单名称规范
- 菜单名必须与FXML文件名对应
- 格式：`homework-panel`（对应`homework-panel.fxml`）

### 权限设置
- 至少选择一个角色权限
- 管理员角色ID为1
- 学生角色ID为2
- 教师角色ID为3

## 🐛 常见问题及解决方案

### 问题1：空指针异常
**现象**：点击提交时出现NullPointerException
**原因**：没有先点击"添加"按钮
**解决**：必须先选择父菜单并点击"添加"，再填写信息

### 问题2：菜单ID冲突
**现象**：提示"主键已经存在"
**解决**：更换一个未使用的菜单ID

### 问题3：菜单不显示
**现象**：添加成功但菜单不显示
**可能原因**：
- FXML文件不存在
- 权限设置错误
- 需要重启应用

### 问题4：权限验证失败
**现象**：提示"请至少选择一个角色权限"
**解决**：确保勾选了至少一个角色复选框

## 🔧 手动数据库添加（备选方案）

如果前端添加失败，可以直接在数据库中添加：

```sql
-- 检查是否已存在
SELECT * FROM menu WHERE id = 43;

-- 如果不存在，插入新记录
INSERT INTO `menu` (id, name, title, pid, user_type_ids) VALUES 
(43, 'homework-panel', '作业管理', 4, '1');

-- 验证插入结果
SELECT * FROM menu WHERE pid = 4;
```

## 📝 完整的菜单信息

添加成功后，数据库中应该有以下记录：

| id | name | title | pid | user_type_ids |
|----|------|-------|-----|---------------|
| 43 | homework-panel | 作业管理 | 4 | 1 |

其中：
- `id`: 43（菜单唯一标识）
- `name`: homework-panel（对应FXML文件名）
- `title`: 作业管理（显示名称）
- `pid`: 4（父菜单ID，教务管理）
- `user_type_ids`: 1（管理员权限）

## ✅ 验证清单

- [ ] 后端服务正常运行
- [ ] 前端应用正常启动
- [ ] 使用管理员账号登录
- [ ] 能够进入菜单管理界面
- [ ] 成功选择"教务管理"作为父菜单
- [ ] 正确填写所有必填字段
- [ ] 至少选择一个角色权限
- [ ] 提交成功并显示"保存成功"
- [ ] 重启后菜单正常显示
- [ ] 点击菜单能正常打开作业管理界面

按照这个指南操作，应该能够成功添加作业管理菜单。如果仍有问题，请提供具体的错误信息。
