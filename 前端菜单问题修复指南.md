# 前端菜单问题修复指南

## 🔍 问题诊断

### 常见的菜单无法显示的原因：

1. **数据库菜单记录缺失或错误**
2. **FXML文件路径不正确**
3. **用户权限不匹配**
4. **菜单层次结构错误**
5. **前端缓存问题**

## 🛠️ 修复步骤

### 步骤1：检查数据库菜单记录

```sql
-- 查看所有菜单记录
SELECT * FROM menu ORDER BY pid, id;

-- 检查作业管理菜单是否存在
SELECT * FROM menu WHERE name = 'homework-panel';
```

**预期结果**：应该看到ID为43的作业管理菜单记录

### 步骤2：验证菜单层次结构

```sql
-- 查看教务管理下的所有子菜单
SELECT * FROM menu WHERE pid = 4;
```

**预期结果**：
- ID 41: 课程管理
- ID 42: 成绩管理  
- ID 43: 作业管理

### 步骤3：检查用户权限

```sql
-- 查看用户角色
SELECT * FROM user_type;

-- 检查当前用户的角色ID
SELECT u.person_id, u.user_type_id, ut.name 
FROM user u 
JOIN user_type ut ON u.user_type_id = ut.id;
```

**注意**：菜单的`user_type_ids`字段必须包含当前用户的角色ID

### 步骤4：验证FXML文件存在

检查以下文件是否存在：
- `src/main/resources/com/teach/javafx/homework-panel.fxml`

### 步骤5：清除前端缓存

重启JavaFX应用程序，确保重新加载菜单数据。

## 🔧 具体修复操作

### 1. 执行数据库修复脚本

```sql
-- 删除可能存在的错误记录
DELETE FROM menu WHERE id = 43;

-- 重新插入正确的菜单记录
INSERT INTO `menu` (id, name, title, pid, user_type_ids) VALUES 
(43, 'homework-panel', '作业管理', 4, '1');
```

### 2. 验证FXML文件路径

确保文件存在：`src/main/resources/com/teach/javafx/homework-panel.fxml`

### 3. 检查JavaFX控制器

确保控制器类存在：`src/main/java/com/teach/javafx/controller/HomeworkController.java`

### 4. 重启应用程序

1. 停止后端Spring Boot应用
2. 停止前端JavaFX应用
3. 重新启动后端
4. 重新启动前端

## 🐛 调试技巧

### 1. 查看后端日志

检查后端控制台是否有菜单加载相关的错误信息。

### 2. 前端调试

在`MainFrameController.initialize()`方法中添加调试代码：

```java
@FXML
public void initialize() {
    handler = new ChangePanelHandler();
    DataResponse res = HttpRequestUtil.request("/api/base/getMenuList", new DataRequest());
    List<Map> mList = (List<Map>) res.getData();
    
    // 添加调试代码
    System.out.println("菜单数据: " + mList);
    for (Map menu : mList) {
        System.out.println("菜单: " + menu.get("title") + ", 子菜单: " + menu.get("sList"));
    }
    
    initMenuBar(mList);
    initMenuTree(mList);
    // ... 其他代码
}
```

### 3. 检查API响应

使用Postman或浏览器直接访问菜单API：
```
POST http://localhost:22223/api/base/getMenuList
Content-Type: application/json

{}
```

## 📋 检查清单

- [ ] 数据库中存在ID为43的作业管理菜单记录
- [ ] 菜单的pid字段为4（教务管理）
- [ ] 菜单的user_type_ids包含当前用户的角色ID
- [ ] FXML文件存在且路径正确
- [ ] JavaFX控制器类存在
- [ ] 后端API正常返回菜单数据
- [ ] 前端能够正确解析菜单数据
- [ ] 应用程序已重启

## 🚨 常见错误

### 错误1：菜单ID冲突
**症状**：菜单不显示或显示错误
**解决**：检查菜单ID是否与现有菜单冲突，使用唯一的ID

### 错误2：权限不匹配
**症状**：菜单对某些用户不可见
**解决**：确保user_type_ids包含正确的角色ID

### 错误3：FXML文件路径错误
**症状**：点击菜单时出现文件未找到错误
**解决**：检查FXML文件路径和文件名是否正确

### 错误4：父菜单不存在
**症状**：子菜单不显示
**解决**：确保父菜单（教务管理，ID=4）存在

## 💡 最佳实践

1. **菜单ID规划**：使用有序的ID，避免冲突
2. **权限设计**：合理设置user_type_ids，支持多角色
3. **文件命名**：FXML文件名与菜单name字段保持一致
4. **测试验证**：每次添加菜单后都要测试各个角色的访问权限

按照这个指南逐步检查和修复，应该能够解决菜单显示问题。
