# 作业管理功能实现说明

## 功能概述
已成功实现作业管理的完整增删改查功能，包含学生姓名、学号、班级、专业、所选择的课程、作业内容、时间等信息。学生和课程通过下拉框从现有数据表中选择，界面风格模仿课程管理设计。

## 已创建的文件

### 后端文件

#### 1. 实体类
- **文件位置**: `java-server/src/main/java/cn/edu/sdu/java/server/models/Homework.java`
- **功能**: 作业表实体类，包含以下字段：
  - `homeworkId`: 作业主键
  - `student`: 关联学生对象
  - `course`: 关联课程对象
  - `content`: 作业内容
  - `assignTime`: 布置时间
  - `dueTime`: 截止时间
  - `submitTime`: 提交时间
  - `status`: 作业状态（0-未提交, 1-已提交, 2-已批改）

#### 2. 数据访问层
- **文件位置**: `java-server/src/main/java/cn/edu/sdu/java/server/repositorys/HomeworkRepository.java`
- **功能**: 作业数据访问接口，提供：
  - 根据学生/课程信息模糊查询
  - 根据学生ID查询作业
  - 根据课程ID查询作业
  - 分页查询支持

#### 3. 业务逻辑层
- **文件位置**: `java-server/src/main/java/cn/edu/sdu/java/server/services/HomeworkService.java`
- **功能**: 作业业务逻辑服务，提供：
  - `getHomeworkList()`: 获取作业列表
  - `homeworkSave()`: 保存作业信息（新增/修改）
  - `homeworkDelete()`: 删除作业
  - `getHomeworkInfo()`: 获取作业详细信息

#### 4. REST API控制器
- **文件位置**: `java-server/src/main/java/cn/edu/sdu/java/server/controllers/HomeworkController.java`
- **功能**: 提供作业管理的REST API接口：
  - `POST /api/homework/getHomeworkList`: 获取作业列表
  - `POST /api/homework/homeworkSave`: 保存作业信息
  - `POST /api/homework/homeworkDelete`: 删除作业
  - `POST /api/homework/getHomeworkInfo`: 获取作业详细信息

### 前端文件

#### 5. JavaFX控制器
- **文件位置**: `java-server/src/main/java/com/teach/javafx/controller/HomeworkController.java`
- **功能**: JavaFX界面控制器，实现：
  - 作业列表显示和管理
  - 学生和课程下拉框选择
  - 作业添加、编辑、删除功能
  - 查询结果在独立窗口显示
  - 完整的用户交互逻辑

#### 6. FXML界面布局
- **文件位置**: `java-server/src/main/resources/com/teach/javafx/homework-panel.fxml`
- **功能**: 作业管理界面布局，包含：
  - 查询区域（支持学号、姓名、课程编号或名称查询）
  - 操作区域（学生选择、课程选择、作业内容输入、截止时间设置）
  - 表格显示区域（显示所有作业信息）
  - 美观的界面设计，与课程管理风格一致

### 数据库文件

#### 7. 数据库建表脚本
- **文件位置**: `java-server/homework-table.sql`
- **功能**: 包含：
  - `course_selection`表创建（选课管理支持）
  - `homework`表创建（作业管理核心表）
  - 示例数据插入
  - 菜单项添加（选课管理和作业管理）

## 功能特点

### 1. 完整的CRUD操作
- ✅ **创建**: 添加新的作业记录
- ✅ **读取**: 查询和显示作业列表
- ✅ **更新**: 编辑现有作业信息
- ✅ **删除**: 删除作业记录

### 2. 用户友好的界面
- ✅ 学生和课程通过下拉框选择，显示详细信息
- ✅ 查询结果在独立窗口显示，可关闭返回主界面
- ✅ 编辑功能通过弹出对话框实现
- ✅ 确认删除对话框防止误操作

### 3. 数据完整性
- ✅ 外键约束确保数据一致性
- ✅ 输入验证防止无效数据
- ✅ 状态管理（未提交/已提交/已批改）

### 4. 界面设计
- ✅ 模仿课程管理的界面风格
- ✅ 响应式布局，适配不同屏幕尺寸
- ✅ 美观的按钮和表格设计
- ✅ 统一的颜色主题和字体样式

## 数据库表结构

### homework表
```sql
CREATE TABLE `homework` (
  `homework_id` int NOT NULL AUTO_INCREMENT,
  `student_id` int DEFAULT NULL,
  `course_id` int DEFAULT NULL,
  `content` varchar(1000) DEFAULT NULL,
  `assign_time` datetime DEFAULT NULL,
  `due_time` datetime DEFAULT NULL,
  `submit_time` datetime DEFAULT NULL,
  `status` varchar(2) DEFAULT '0',
  PRIMARY KEY (`homework_id`),
  FOREIGN KEY (`student_id`) REFERENCES `student` (`person_id`),
  FOREIGN KEY (`course_id`) REFERENCES `course` (`course_id`)
);
```

## 使用说明

### 1. 数据库配置
1. 执行 `homework-table.sql` 脚本创建必要的数据库表
2. 确保数据库连接配置正确

### 2. 启动应用
1. 启动后端Spring Boot应用
2. 启动JavaFX前端应用
3. 在菜单中找到"教务管理" -> "作业管理"

### 3. 功能操作
- **添加作业**: 选择学生和课程，输入作业内容和截止时间，点击"添加作业"
- **查询作业**: 在查询框中输入学号、姓名、课程编号或名称，点击"查询作业"
- **编辑作业**: 点击表格中的"编辑"按钮，在弹出对话框中修改信息
- **删除作业**: 点击表格中的"删除"按钮，确认后删除记录

## 技术栈
- **后端**: Spring Boot, JPA/Hibernate, MySQL
- **前端**: JavaFX, FXML
- **数据库**: MySQL
- **架构**: MVC模式，RESTful API

## 注意事项
1. 确保学生表和课程表中有数据，否则下拉框将为空
2. 时间格式为 `yyyy-MM-dd HH:mm:ss`
3. 作业状态：0-未提交，1-已提交，2-已批改
4. 删除操作不可恢复，请谨慎操作

## 扩展功能建议
- 添加作业文件上传功能
- 实现作业批改和评分功能
- 添加作业提醒和通知功能
- 支持批量操作（批量删除、批量修改状态等）
