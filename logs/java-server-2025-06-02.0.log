2025-06-02 16:38:00,743 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-02 16:38:00,788 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 21.0.5 with PID 22976 (D:\QQ文件\高级程序开发\java-server\target\classes started by ASUS in D:\QQ文件\高级程序开发)
2025-06-02 16:38:00,788 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-02 16:38:01,450 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 16:38:01,451 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-02 16:38:01,614 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 157 ms. Found 16 JPA repository interfaces.
2025-06-02 16:38:01,633 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 16:38:01,634 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-02 16:38:01,657 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,657 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseSelectionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,657 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,658 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,660 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,660 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,660 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,660 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 16:38:01,660 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-02 16:38:02,140 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-02 16:38:02,152 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-02 16:38:02,153 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-02 16:38:02,153 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-02 16:38:02,197 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-02 16:38:02,198 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1377 ms
2025-06-02 16:38:02,315 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-02 16:38:02,375 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-02 16:38:02,410 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-02 16:38:02,630 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-02 16:38:02,651 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-02 16:38:07,428 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7f1fec57
2025-06-02 16:38:07,430 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-02 16:38:07,527 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-02 16:38:08,291 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-02 16:38:08,668 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-02 16:38:08,884 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-02 16:38:09,381 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-02 16:38:09,581 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-02 16:38:09,581 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-02 16:38:10,476 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-02 16:38:10,487 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-02 16:38:10,494 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 10.211 seconds (process running for 10.782)
2025-06-02 16:38:10,496 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@403acbdb]
2025-06-02 16:38:10,496 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-02 16:38:10,611 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-02 16:38:14,976 [http-nio-22223-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-02 16:38:14,977 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-02 16:38:14,978 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 0 ms
2025-06-02 16:38:15,643 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-02 16:38:15,0.228
2025-06-02 16:38:19,087 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 16:38:19,0.041
2025-06-02 16:38:19,130 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 16:38:19,0.039
2025-06-02 16:38:19,168 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 16:38:19,0.033
2025-06-02 16:38:20,304 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-02 16:38:20,0.071
2025-06-02 16:38:20,414 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-02 16:38:20,0.053
2025-06-02 16:38:20,455 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-02 16:38:20,0.036
2025-06-02 17:17:38,998 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=5m30s961ms677µs100ns).
2025-06-02 17:39:30,811 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m51s732ms966µs500ns).
2025-06-02 17:52:47,387 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m46s463ms730µs700ns).
2025-06-02 18:29:10,090 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=30m52s616ms346µs200ns).
2025-06-02 18:54:17,459 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-02 18:54:17,0.096
2025-06-02 18:54:20,780 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 18:54:20,0.037
2025-06-02 18:54:20,836 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 18:54:20,0.049
2025-06-02 18:54:20,893 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 18:54:20,0.049
2025-06-02 18:54:21,888 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-02 18:54:21,0.057
2025-06-02 18:54:21,952 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-02 18:54:21,0.058
2025-06-02 18:54:21,994 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-02 18:54:21,0.036
2025-06-02 18:54:25,177 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getStudentOptions,admin,2025-06-02 18:54:25,0.094
2025-06-02 18:54:25,242 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseOptions,admin,2025-06-02 18:54:25,0.06
2025-06-02 18:54:25,321 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseSelectionList,admin,2025-06-02 18:54:25,0.074
2025-06-02 19:15:40,084 [SpringApplicationShutdownHook] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-02 19:15:40,091 [tomcat-shutdown] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Graceful shutdown complete
2025-06-02 19:15:40,124 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-02 19:15:40,129 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown initiated...
2025-06-02 19:15:40,131 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown completed.
2025-06-02 22:14:12,877 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-02 22:14:12,915 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 21.0.5 with PID 28824 (D:\QQ文件\高级程序开发\java-server\target\classes started by ASUS in D:\QQ文件\高级程序开发)
2025-06-02 22:14:12,916 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-02 22:14:13,526 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 22:14:13,528 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-02 22:14:13,691 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 155 ms. Found 16 JPA repository interfaces.
2025-06-02 22:14:13,712 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 22:14:13,713 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-02 22:14:13,726 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,727 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseSelectionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,727 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,727 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,727 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,727 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,728 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,729 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,729 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 22:14:13,729 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-02 22:14:14,255 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-02 22:14:14,264 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-02 22:14:14,266 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-02 22:14:14,266 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-02 22:14:14,314 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-02 22:14:14,314 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1368 ms
2025-06-02 22:14:14,417 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-02 22:14:14,477 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-02 22:14:14,516 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-02 22:14:14,750 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-02 22:14:14,773 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-02 22:14:19,488 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4a2d66e
2025-06-02 22:14:19,489 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-02 22:14:19,572 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-02 22:14:20,377 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-02 22:14:20,908 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-02 22:14:21,130 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-02 22:14:21,635 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-02 22:14:21,867 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-02 22:14:21,868 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-02 22:14:22,861 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-02 22:14:22,875 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-02 22:14:22,881 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 10.43 seconds (process running for 11.081)
2025-06-02 22:14:22,884 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@4f79a086]
2025-06-02 22:14:22,884 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-02 22:14:23,221 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-02 22:14:29,586 [http-nio-22223-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-02 22:14:29,586 [http-nio-22223-exec-3] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-02 22:14:29,587 [http-nio-22223-exec-3] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 1 ms
2025-06-02 22:14:30,369 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-02 22:14:30,0.277
2025-06-02 22:14:38,142 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getStudentOptions,admin,2025-06-02 22:14:37,0.154
2025-06-02 22:14:38,244 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseOptions,admin,2025-06-02 22:14:38,0.095
2025-06-02 22:14:38,359 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseSelectionList,admin,2025-06-02 22:14:38,0.109
2025-06-02 22:15:10,223 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-02 22:15:10,0.091
2025-06-02 22:15:10,293 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:15:10,0.063
2025-06-02 22:15:11,662 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:15:11,0.073
2025-06-02 22:15:11,714 [http-nio-22223-exec-6] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\96.jpg (系统找不到指定的路径。)
2025-06-02 22:15:11,715 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:15:11,0.043
2025-06-02 22:15:14,425 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentDelete,admin,2025-06-02 22:15:14,0.389
2025-06-02 22:15:14,512 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-02 22:15:14,0.065
2025-06-02 22:15:16,336 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:15:16,0.084
2025-06-02 22:15:16,385 [http-nio-22223-exec-2] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\95.jpg (系统找不到指定的路径。)
2025-06-02 22:15:16,385 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:15:16,0.042
2025-06-02 22:15:19,546 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 22:15:19,0.067
2025-06-02 22:15:19,602 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 22:15:19,0.051
2025-06-02 22:15:19,666 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 22:15:19,0.06
2025-06-02 22:15:21,451 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getStudentItemOptionList,admin,2025-06-02 22:15:21,0.128
2025-06-02 22:15:21,547 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getCourseItemOptionList,admin,2025-06-02 22:15:21,0.091
2025-06-02 22:15:21,615 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/score/getScoreList,admin,2025-06-02 22:15:21,0.063
2025-06-02 22:32:18,331 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:32:18,0.072
2025-06-02 22:32:18,369 [http-nio-22223-exec-6] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\97.jpg (系统找不到指定的路径。)
2025-06-02 22:32:18,370 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:32:18,0.032
2025-06-02 22:32:27,214 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m37s544ms576µs500ns).
2025-06-02 22:33:07,508 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/studentEditSave,admin,2025-06-02 22:33:06,0.905
2025-06-02 22:33:07,677 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentList,admin,2025-06-02 22:33:07,0.146
2025-06-02 22:33:10,035 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:09,0.089
2025-06-02 22:33:10,083 [http-nio-22223-exec-4] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\104.jpg (系统找不到指定的路径。)
2025-06-02 22:33:10,084 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:33:10,0.044
2025-06-02 22:33:17,024 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/uploadPhoto,admin,2025-06-02 22:33:16,0.047
2025-06-02 22:33:20,600 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:20,0.141
2025-06-02 22:33:20,670 [http-nio-22223-exec-7] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\82.jpg (系统找不到指定的路径。)
2025-06-02 22:33:20,671 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:33:20,0.057
2025-06-02 22:33:23,780 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberList1,admin,2025-06-02 22:33:23,0.065
2025-06-02 22:33:23,846 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:33:23,0.058
2025-06-02 22:33:26,161 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:26,0.071
2025-06-02 22:33:26,206 [http-nio-22223-exec-9] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\2.jpg (系统找不到指定的路径。)
2025-06-02 22:33:26,206 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:33:26,0.038
2025-06-02 22:33:28,120 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberList1,admin,2025-06-02 22:33:28,0.059
2025-06-02 22:33:28,167 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:33:28,0.04
2025-06-02 22:33:29,530 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberInfo,admin,2025-06-02 22:33:29,0.044
2025-06-02 22:33:29,937 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberInfo,admin,2025-06-02 22:33:29,0.046
2025-06-02 22:33:30,358 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberInfo,admin,2025-06-02 22:33:30,0.041
2025-06-02 22:33:30,800 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberInfo,admin,2025-06-02 22:33:30,0.046
2025-06-02 22:33:33,973 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/familyMemberDelete,admin,2025-06-02 22:33:33,0.163
2025-06-02 22:33:34,045 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberList1,admin,2025-06-02 22:33:33,0.051
2025-06-02 22:33:36,450 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberInfo,admin,2025-06-02 22:33:36,0.05
2025-06-02 22:33:40,298 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:40,0.118
2025-06-02 22:33:42,345 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:42,0.125
2025-06-02 22:33:42,448 [http-nio-22223-exec-8] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\2.jpg (系统找不到指定的路径。)
2025-06-02 22:33:42,449 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:33:42,0.097
2025-06-02 22:33:42,985 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:42,0.068
2025-06-02 22:33:44,544 [http-nio-22223-exec-9] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberList1,admin,2025-06-02 22:33:44,0.082
2025-06-02 22:33:44,630 [http-nio-22223-exec-6] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:33:44,0.081
2025-06-02 22:33:47,555 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/student/getStudentInfo,admin,2025-06-02 22:33:47,0.091
2025-06-02 22:33:47,610 [http-nio-22223-exec-7] ERROR c.e.sdu.java.server.services.BaseService           - \teach-2025\photo\82.jpg (系统找不到指定的路径。)
2025-06-02 22:33:47,612 [http-nio-22223-exec-7] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getFileByteData,admin,2025-06-02 22:33:47,0.052
2025-06-02 22:33:48,695 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/family-member/getFamilyMemberList1,admin,2025-06-02 22:33:48,0.062
2025-06-02 22:33:48,767 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:33:48,0.068
2025-06-02 22:34:01,388 [http-nio-22223-exec-8] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/teacher/getTeacherList,admin,2025-06-02 22:34:01,0.052
2025-06-02 22:34:01,435 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getDictionaryOptionItemList,admin,2025-06-02 22:34:01,0.041
2025-06-02 22:47:33,634 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m6s310ms714µs700ns).
2025-06-02 23:31:26,733 [background-preinit] INFO  o.h.validator.internal.util.Version                - HV000001: Hibernate Validator 8.0.2.Final
2025-06-02 23:31:26,776 [main] INFO  c.e.s.java.server.JavaServerApplication            - Starting JavaServerApplication using Java 21.0.5 with PID 19328 (D:\QQ文件\高级程序开发\java-server\target\classes started by ASUS in D:\QQ文件\高级程序开发)
2025-06-02 23:31:26,776 [main] INFO  c.e.s.java.server.JavaServerApplication            - No active profile set, falling back to 1 default profile: "default"
2025-06-02 23:31:27,421 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 23:31:27,424 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-02 23:31:27,571 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 140 ms. Found 16 JPA repository interfaces.
2025-06-02 23:31:27,587 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-02 23:31:27,588 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-02 23:31:27,604 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,604 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.CourseSelectionRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.DictionaryInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FamilyMemberRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.FeeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.MenuInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ModifyLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.PersonRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.ScoreRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,605 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StatisticsDayRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,606 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.StudentStatisticsRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.SystemInfoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.TeacherRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport  - Spring Data Redis - Could not safely identify store assignment for repository candidate interface cn.edu.sdu.java.server.repositorys.UserTypeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-02 23:31:27,610 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate          - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-02 23:31:28,073 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat initialized with port 22223 (http)
2025-06-02 23:31:28,091 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Initializing ProtocolHandler ["http-nio-22223"]
2025-06-02 23:31:28,093 [main] INFO  o.apache.catalina.core.StandardService             - Starting service [Tomcat]
2025-06-02 23:31:28,093 [main] INFO  org.apache.catalina.core.StandardEngine            - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-02 23:31:28,137 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring embedded WebApplicationContext
2025-06-02 23:31:28,137 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext     - Root WebApplicationContext: initialization completed in 1330 ms
2025-06-02 23:31:28,239 [main] INFO  o.hibernate.jpa.internal.util.LogHelper            - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-02 23:31:28,291 [main] INFO  org.hibernate.Version                              - HHH000412: Hibernate ORM core version 6.6.5.Final
2025-06-02 23:31:28,322 [main] INFO  o.h.c.internal.RegionFactoryInitiator              - HHH000026: Second-level cache disabled
2025-06-02 23:31:28,546 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo                - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-02 23:31:28,569 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Starting...
2025-06-02 23:31:33,250 [main] INFO  com.zaxxer.hikari.pool.HikariPool                  - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@13137835
2025-06-02 23:31:33,251 [main] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Start completed.
2025-06-02 23:31:33,324 [main] INFO  org.hibernate.orm.connections.pooling              - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'HikariDataSource (HikariPool-1)']
	Database driver: undefined/unknown
	Database version: 8.0.35
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-02 23:31:34,072 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator                 - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-02 23:31:34,358 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-02 23:31:34,558 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory               - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-02 23:31:35,050 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-02 23:31:35,259 [main] INFO  o.s.s.c.a.a.c.InitializeAuthenticationProviderBeanManagerConfigurer$InitializeAuthenticationProviderManagerConfigurer - Global AuthenticationManager configured with AuthenticationProvider bean with name authenticationProvider
2025-06-02 23:31:35,259 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used by Spring Security for automatically configuring username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider. If the current configuration is intentional, to turn off this warning, increase the logging level of 'org.springframework.security.config.annotation.authentication.configuration.InitializeUserDetailsBeanManagerConfigurer' to ERROR
2025-06-02 23:31:36,168 [main] INFO  o.apache.coyote.http11.Http11NioProtocol           - Starting ProtocolHandler ["http-nio-22223"]
2025-06-02 23:31:36,179 [main] INFO  o.s.b.w.embedded.tomcat.TomcatWebServer            - Tomcat started on port 22223 (http) with context path '/'
2025-06-02 23:31:36,185 [main] INFO  c.e.s.java.server.JavaServerApplication            - Started JavaServerApplication in 9.904 seconds (process running for 10.451)
2025-06-02 23:31:36,186 [main] INFO  c.e.s.j.server.SystemApplicationListener           - org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@4bd2a8b0]
2025-06-02 23:31:36,187 [main] INFO  c.e.s.j.server.SystemApplicationListener           - SystemInitStart
2025-06-02 23:31:36,322 [main] INFO  c.e.s.j.server.SystemApplicationListener           - systemInitEnd
2025-06-02 23:31:52,551 [http-nio-22223-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/]                 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-02 23:31:52,552 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Initializing Servlet 'dispatcherServlet'
2025-06-02 23:31:52,552 [http-nio-22223-exec-1] INFO  o.s.web.servlet.DispatcherServlet                  - Completed initialization in 0 ms
2025-06-02 23:31:53,253 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/base/getMenuList,admin,2025-06-02 23:31:53,0.237
2025-06-02 23:31:56,364 [http-nio-22223-exec-3] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 23:31:56,0.057
2025-06-02 23:31:56,430 [http-nio-22223-exec-4] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 23:31:56,0.06
2025-06-02 23:31:56,485 [http-nio-22223-exec-5] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/course/getCourseList,admin,2025-06-02 23:31:56,0.05
2025-06-02 23:31:59,342 [http-nio-22223-exec-10] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getStudentOptions,admin,2025-06-02 23:31:59,0.181
2025-06-02 23:31:59,448 [http-nio-22223-exec-1] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseOptions,admin,2025-06-02 23:31:59,0.1
2025-06-02 23:31:59,571 [http-nio-22223-exec-2] INFO  c.e.s.j.s.c.JwtAuthenticationFilter                - /api/courseSelection/getCourseSelectionList,admin,2025-06-02 23:31:59,0.119
2025-06-02 23:32:42,036 [SpringApplicationShutdownHook] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Commencing graceful shutdown. Waiting for active requests to complete
2025-06-02 23:32:42,040 [tomcat-shutdown] INFO  o.s.b.w.embedded.tomcat.GracefulShutdown           - Graceful shutdown complete
2025-06-02 23:32:42,053 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean     - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-02 23:32:42,055 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown initiated...
2025-06-02 23:32:42,059 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource                 - HikariPool-1 - Shutdown completed.
